#!/usr/bin/env python3
"""
误报图像Web浏览器
可以分页浏览每天的误报图像和结果，支持查看原图和标注图。
"""

from flask import Flask, render_template_string, request, send_from_directory, abort
from database_manager import DatabaseManager
from image_storage import ImageStorage
from datetime import datetime
import os

app = Flask(__name__)

db_manager = DatabaseManager()
image_storage = ImageStorage()

PAGE_SIZE = 20

TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>误报图像浏览</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .report-list { display: flex; flex-wrap: wrap; gap: 16px; }
        .report-card { border: 1px solid #ccc; border-radius: 8px; padding: 12px; width: 320px; background: #fafbfc; }
        .img-preview { width: 140px; height: 140px; object-fit: contain; border: 1px solid #eee; margin-right: 8px; }
        .img-row { display: flex; align-items: center; }
        .meta { font-size: 13px; color: #666; margin-top: 4px; }
        .pagination { margin-top: 24px; }
        .pagination a { margin: 0 4px; text-decoration: none; color: #007bff; }
        .pagination .current { font-weight: bold; color: #222; }
    </style>
</head>
<body>
    <h2>误报图像浏览</h2>
    <form method="get">
        日期: <input type="date" name="date" value="{{ date }}">
        <button type="submit">筛选</button>
    </form>
    <div class="report-list">
        {% for report in reports %}
        <div class="report-card">
            <div class="img-row">
                {% if report.original_image_url %}
                <img src="{{ report.original_image_url }}" class="img-preview" title="原图">
                {% endif %}
                {% if report.annotated_image_url %}
                <img src="{{ report.annotated_image_url }}" class="img-preview" title="标注图">
                {% endif %}
            </div>
            <div class="meta">
                <div>报告ID: {{ report.report_id }}</div>
                <div>会话ID: {{ report.session_id }}</div>
                <div>类型: {{ report.false_positive_type }}</div>
                <div>状态: {{ report.status }}</div>
                <div>上报时间: {{ report.created_at }}</div>
            </div>
        </div>
        {% endfor %}
    </div>
    <div class="pagination">
        {% if page > 1 %}<a href="?date={{ date }}&page={{ page-1 }}">上一页</a>{% endif %}
        <span class="current">第 {{ page }} 页</span>
        {% if has_next %}<a href="?date={{ date }}&page={{ page+1 }}">下一页</a>{% endif %}
    </div>
</body>
</html>
'''

@app.route('/')
def index():
    # 获取日期和分页参数
    date_str = request.args.get('date')
    page = int(request.args.get('page', 1))
    offset = (page - 1) * PAGE_SIZE

    # 构建查询条件
    where = []
    params = []
    if date_str:
        where.append("DATE(fpr.created_at) = ?")
        params.append(date_str)
    where_clause = 'WHERE ' + ' AND '.join(where) if where else ''

    # 查询误报记录
    query = f'''
        SELECT fpr.*, ir.original_image_path, ir.annotated_image_path, ir.has_annotated_image
        FROM false_positive_reports fpr
        LEFT JOIN inference_records ir ON fpr.session_id = ir.session_id
        {where_clause}
        ORDER BY fpr.created_at DESC
        LIMIT ? OFFSET ?
    '''
    params += [PAGE_SIZE + 1, offset]  # 多查一条判断是否有下一页
    with db_manager.get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute(query, params)
        rows = cursor.fetchall()

    reports = []
    for row in rows[:PAGE_SIZE]:
        original_url = None
        annotated_url = None
        if row['original_image_path'] and image_storage.image_exists(row['original_image_path']):
            original_url = f"/image/original/{row['original_image_path']}"
        if row['annotated_image_path'] and row['has_annotated_image'] and image_storage.image_exists(row['annotated_image_path']):
            annotated_url = f"/image/annotated/{row['annotated_image_path']}"
        reports.append({
            'report_id': row['report_id'],
            'session_id': row['session_id'],
            'false_positive_type': row['false_positive_type'],
            'status': row['status'],
            'created_at': row['created_at'],
            'original_image_url': original_url,
            'annotated_image_url': annotated_url
        })
    has_next = len(rows) > PAGE_SIZE
    return render_template_string(TEMPLATE, reports=reports, page=page, has_next=has_next, date=date_str or '')

@app.route('/image/<img_type>/<path:rel_path>')
def serve_image(img_type, rel_path):
    # 安全检查
    if '..' in rel_path or rel_path.startswith('/'):
        abort(404)
    base_dir = os.path.join(image_storage.base_path, img_type)
    abs_path = os.path.join(base_dir, rel_path)
    if not os.path.isfile(abs_path):
        abort(404)
    return send_from_directory(base_dir, rel_path)

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5008, debug=True) 