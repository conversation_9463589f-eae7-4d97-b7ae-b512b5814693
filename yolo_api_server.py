#!/usr/bin/env python3
"""
YOLO推理API服务器
基于Flask的简单API服务，提供图像上传和YOLO模型推理功能
支持单文件部署，便于在不同环境中运行

使用方法:
1. 安装依赖: pip install flask ultralytics pillow
2. 运行服务: python yolo_api_server.py
3. 访问 http://localhost:5000 查看API文档

API端点:
- GET /: 显示API使用说明
- POST /predict: 上传图像进行推理
- GET /health: 健康检查
"""

import os
import io
import json
import logging
import traceback
import time
from datetime import datetime
from pathlib import Path
import base64
from logging.handlers import RotatingFileHandler

import cv2
import numpy as np
from PIL import Image
from flask import Flask, request, jsonify, render_template_string, send_from_directory, abort
from ultralytics import YOLO

# 导入数据库和图像存储模块
from database_manager import DatabaseManager
from image_storage import ImageStorage

# 导入日志配置模块
from logging_config import setup_logging

# 配置日志
# def setup_logging():
#     """设置日志配置，支持文件滚动"""
#     # 创建日志文件夹
#     log_dir = Path("logs")
#     log_dir.mkdir(exist_ok=True)

#     # 创建logger
#     logger = logging.getLogger(__name__)
#     logger.setLevel(logging.INFO)

#     # 清除已有的处理器
#     logger.handlers.clear()

#     # 创建文件处理器 - 滚动日志文件
#     log_file = log_dir / "yolo_api.log"
#     file_handler = RotatingFileHandler(
#         log_file,
#         maxBytes=10 * 1024 * 1024,  # 10MB
#         backupCount=10,  # 保留10个备份文件
#         encoding='utf-8'
#     )
#     file_handler.setLevel(logging.INFO)

#     # 创建控制台处理器
#     console_handler = logging.StreamHandler()
#     console_handler.setLevel(logging.INFO)

#     # 创建格式器
#     formatter = logging.Formatter(
#         '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
#         datefmt='%Y-%m-%d %H:%M:%S'
#     )
#     file_handler.setFormatter(formatter)
#     console_handler.setFormatter(formatter)

#     # 添加处理器到logger
#     logger.addHandler(file_handler)
#     logger.addHandler(console_handler)

#     return logger

# 设置日志
logger = setup_logging()

# 统计配置 - 可以在这里配置需要统计的接口
STATS_CONFIG = {
    'predict': {
        'name': '图像推理',
        'method': 'POST',
        'description': '上传图像进行目标检测推理',
        'enabled': True
    },
    'health': {
        'name': '健康检查',
        'method': 'GET',
        'description': '检查服务状态和模型加载情况',
        'enabled': False  # 设为False表示不在统计页面显示
    }
}

# 接口调用统计 - 只初始化启用的接口
api_stats = {}
for endpoint, config in STATS_CONFIG.items():
    if config['enabled']:
        api_stats[endpoint] = {'total': 0, 'success': 0, 'errors': 0}

# 推理时间统计 - 排除预热推理
inference_timing_stats = {
    'total_inferences': 0,
    'total_time_ms': 0,
    'preprocess_times': [],
    'inference_times': [],
    'postprocess_times': [],
    'annotation_times': [],
    'total_times': [],
    'min_total_time': float('inf'),
    'max_total_time': 0,
    'avg_total_time': 0,
    'min_inference_time': float('inf'),
    'max_inference_time': 0,
    'avg_inference_time': 0
}

def update_api_stats(endpoint, success=True):
    """更新API调用统计"""
    # 更新内存中的统计（用于实时显示）
    if endpoint in api_stats:
        api_stats[endpoint]['total'] += 1
        if success:
            api_stats[endpoint]['success'] += 1
        else:
            api_stats[endpoint]['errors'] += 1
    
    # 更新数据库中的统计（用于持久化）
    if db_manager:
        try:
            method = STATS_CONFIG.get(endpoint, {}).get('method', 'GET')
            db_manager.update_api_call_statistics(endpoint, method, success)
        except Exception as e:
            logger.error(f"更新数据库API统计失败: {e}")

def get_success_rate(endpoint):
    """计算成功率"""
    stats = api_stats.get(endpoint, {'total': 0, 'success': 0})
    if stats['total'] == 0:
        return 0.0
    return round((stats['success'] / stats['total']) * 100, 2)

def get_enabled_stats():
    """获取启用的统计接口"""
    return {k: v for k, v in STATS_CONFIG.items() if v['enabled']}

def update_inference_timing_stats(timing_info):
    """更新推理时间统计（排除预热推理）"""
    global inference_timing_stats

    try:
        # 提取时间信息
        preprocess_time = timing_info.get('preprocess_time', 0)
        inference_time = timing_info.get('inference_time', 0)
        postprocess_time = timing_info.get('postprocess_time', 0)
        annotation_time = timing_info.get('image_annotation_time', 0)
        total_time = timing_info.get('total_time', 0)

        # 更新统计
        inference_timing_stats['total_inferences'] += 1
        inference_timing_stats['total_time_ms'] += total_time

        # 添加到时间列表（保留最近100次记录）
        max_records = 100
        inference_timing_stats['preprocess_times'].append(preprocess_time)
        inference_timing_stats['inference_times'].append(inference_time)
        inference_timing_stats['postprocess_times'].append(postprocess_time)
        inference_timing_stats['annotation_times'].append(annotation_time)
        inference_timing_stats['total_times'].append(total_time)

        # 保持列表大小
        for key in ['preprocess_times', 'inference_times', 'postprocess_times', 'annotation_times', 'total_times']:
            if len(inference_timing_stats[key]) > max_records:
                inference_timing_stats[key] = inference_timing_stats[key][-max_records:]

        # 更新最小/最大值
        inference_timing_stats['min_total_time'] = min(inference_timing_stats['min_total_time'], total_time)
        inference_timing_stats['max_total_time'] = max(inference_timing_stats['max_total_time'], total_time)
        inference_timing_stats['min_inference_time'] = min(inference_timing_stats['min_inference_time'], inference_time)
        inference_timing_stats['max_inference_time'] = max(inference_timing_stats['max_inference_time'], inference_time)

        # 计算平均值
        if inference_timing_stats['total_inferences'] > 0:
            inference_timing_stats['avg_total_time'] = round(
                inference_timing_stats['total_time_ms'] / inference_timing_stats['total_inferences'], 2
            )
            inference_timing_stats['avg_inference_time'] = round(
                sum(inference_timing_stats['inference_times']) / len(inference_timing_stats['inference_times']), 2
            )

    except Exception as e:
        logger.error(f"更新推理时间统计失败: {e}")

def get_timing_statistics():
    """获取推理时间统计信息"""
    global inference_timing_stats

    if inference_timing_stats['total_inferences'] == 0:
        return {
            'total_inferences': 0,
            'message': '暂无推理数据（预热推理不计入统计）'
        }

    # 计算最近的统计信息
    recent_times = inference_timing_stats['total_times'][-10:] if len(inference_timing_stats['total_times']) >= 10 else inference_timing_stats['total_times']
    recent_inference_times = inference_timing_stats['inference_times'][-10:] if len(inference_timing_stats['inference_times']) >= 10 else inference_timing_stats['inference_times']

    stats = {
        'total_inferences': inference_timing_stats['total_inferences'],
        'overall': {
            'avg_total_time_ms': inference_timing_stats['avg_total_time'],
            'min_total_time_ms': round(inference_timing_stats['min_total_time'], 2) if inference_timing_stats['min_total_time'] != float('inf') else 0,
            'max_total_time_ms': round(inference_timing_stats['max_total_time'], 2),
            'avg_inference_time_ms': inference_timing_stats['avg_inference_time'],
            'min_inference_time_ms': round(inference_timing_stats['min_inference_time'], 2) if inference_timing_stats['min_inference_time'] != float('inf') else 0,
            'max_inference_time_ms': round(inference_timing_stats['max_inference_time'], 2)
        },
        'recent_10': {
            'avg_total_time_ms': round(sum(recent_times) / len(recent_times), 2) if recent_times else 0,
            'min_total_time_ms': round(min(recent_times), 2) if recent_times else 0,
            'max_total_time_ms': round(max(recent_times), 2) if recent_times else 0,
            'avg_inference_time_ms': round(sum(recent_inference_times) / len(recent_inference_times), 2) if recent_inference_times else 0
        }
    }

    return stats

def log_response_summary(response_data):
    """记录响应数据摘要，避免打印完整的base64数据"""
    summary = {
        "success": response_data.get("success"),
        "timestamp": response_data.get("timestamp"),
        "filename": response_data.get("filename"),
        "detections_count": response_data.get("detections_count"),
        "timing": response_data.get("timing"),
        "model_path": response_data.get("model_path"),
        "confidence_threshold": response_data.get("confidence_threshold")
    }

    # 如果有标注图像，只记录其大小信息
    if "annotated_image" in response_data:
        image_data = response_data["annotated_image"]
        if image_data and image_data.startswith("data:image/"):
            # 计算base64数据大小
            base64_part = image_data.split(",", 1)[1] if "," in image_data else image_data
            size_kb = len(base64_part.encode('utf-8')) / 1024
            summary["annotated_image_info"] = {
                "format": "base64_jpeg",
                "size_kb": round(size_kb, 2)
            }

    return summary

# Flask应用配置
app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB最大文件大小

# 全局变量
model = None
model_path = None

# 数据库和图像存储管理器
db_manager = None
image_storage = None

# 支持的图像格式
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'tiff', 'webp'}

def allowed_file(filename):
    """检查文件扩展名是否被支持"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def find_model_file():
    """自动查找可用的YOLO模型文件"""
    # 优先级顺序：自定义训练模型 > 预训练模型
    search_paths = [
        # 自定义训练的模型
        "../weights/PACK_AOI_best.pt"
    ]
    
    for pattern in search_paths:
        if '*' in pattern:
            # 使用glob模式匹配
            import glob
            matches = glob.glob(pattern)
            if matches:
                # 选择最新的模型文件
                latest_model = max(matches, key=os.path.getmtime)
                if os.path.exists(latest_model):
                    return latest_model
        else:
            if os.path.exists(pattern):
                return pattern
    
    return None

def initialize_storage_systems():
    """初始化数据库和图像存储系统"""
    global db_manager, image_storage

    try:
        # 初始化数据库管理器
        db_manager = DatabaseManager(
            db_path="yolo_inference.db",
            schema_file="database_schema.sql"
        )
        logger.info("数据库管理器初始化成功")

        # 初始化图像存储管理器
        image_storage = ImageStorage(base_storage_path="image_storage")
        logger.info("图像存储管理器初始化成功")

        return True

    except Exception as e:
        logger.error(f"存储系统初始化失败: {e}")
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        return False

def load_model():
    """加载YOLO模型"""
    global model, model_path

    try:
        model_path = find_model_file()
        if not model_path:
            logger.error("未找到YOLO模型文件！请确保模型文件存在。")
            return False

        logger.info(f"正在加载模型: {model_path}")
        model = YOLO(model_path)

        model_info = {
            "model_path": model_path,
            "model_type": "YOLO",
            "status": "loaded_successfully"
        }
        logger.info(f"模型加载成功: {json.dumps(model_info, ensure_ascii=False, indent=2)}")
        return True

    except Exception as e:
        error_info = {
            "error_type": type(e).__name__,
            "error_message": str(e),
            "model_path": model_path,
            "status": "load_failed"
        }
        logger.error(f"模型加载失败: {json.dumps(error_info, ensure_ascii=False, indent=2)}")
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        return False

def warmup_model():
    """模型预热 - 使用测试图片进行一次推理"""
    global model

    if model is None:
        logger.warning("模型未加载，跳过预热")
        return False

    try:
        # 测试图片路径
        test_image_path = "resource/images/test_image.jpg"

        if not os.path.exists(test_image_path):
            logger.warning(f"测试图片不存在: {test_image_path}，跳过模型预热")
            return False

        logger.info("开始模型预热...")
        warmup_start = time.time()

        # 读取测试图片
        image = Image.open(test_image_path)
        if image.mode != 'RGB':
            image = image.convert('RGB')
        image_array = np.array(image)

        # 执行推理（预热）
        results = model(image_array)

        # 简单处理结果以确保完整的推理流程
        detections = postprocess_results(results, confidence_threshold=0.5)

        warmup_end = time.time()
        warmup_time = round((warmup_end - warmup_start) * 1000, 2)

        warmup_info = {
            "test_image": test_image_path,
            "image_size": {"width": image.width, "height": image.height},
            "detections_count": len(detections),
            "warmup_time_ms": warmup_time,
            "status": "completed"
        }

        logger.info(f"模型预热完成: {json.dumps(warmup_info, ensure_ascii=False, indent=2)}")
        return True

    except Exception as e:
        logger.error(f"模型预热失败: {str(e)}")
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        return False

def preprocess_image(image_data):
    """预处理上传的图像数据"""
    try:
        # 将字节数据转换为PIL图像
        image = Image.open(io.BytesIO(image_data))
        
        # 转换为RGB格式（如果需要）
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # 转换为numpy数组
        image_array = np.array(image)
        
        return image_array, image
        
    except Exception as e:
        logger.error(f"图像预处理失败: {str(e)}")
        raise

def postprocess_results(results, confidence_threshold=0.1):
    """后处理YOLO推理结果"""
    try:
        processed_results = []
        

        for result in results:
            # 获取检测框信息
            boxes = result.boxes
            if boxes is not None:
                for i, box in enumerate(boxes):
                    # 获取置信度
                    confidence = float(box.conf[0])
                    
                    # 过滤低置信度检测
                    if confidence < confidence_threshold:
                        continue
                    
                    # 获取边界框坐标 (x1, y1, x2, y2)
                    x1, y1, x2, y2 = box.xyxy[0].tolist()
                    
                    # 获取类别ID和名称
                    class_id = int(box.cls[0])
                    class_name = result.names[class_id] if result.names else f"class_{class_id}"
                    
                    # 计算中心点和尺寸
                    center_x = (x1 + x2) / 2
                    center_y = (y1 + y2) / 2
                    width = x2 - x1
                    height = y2 - y1
                    
                    detection = {
                        "class_id": class_id,
                        "class_name": class_name,
                        "confidence": round(confidence, 4),
                        "bbox": {
                            "x1": round(x1, 2),
                            "y1": round(y1, 2),
                            "x2": round(x2, 2),
                            "y2": round(y2, 2),
                            "center_x": round(center_x, 2),
                            "center_y": round(center_y, 2),
                            "width": round(width, 2),
                            "height": round(height, 2)
                        }
                    }
                    processed_results.append(detection)
        
        return processed_results
        
    except Exception as e:
        logger.error(f"结果后处理失败: {str(e)}")
        raise

def create_annotated_image(image, detections):
    """在图像上绘制检测结果"""
    try:
        # 复制图像以避免修改原图
        annotated_image = image.copy()
        
        for detection in detections:
            bbox = detection["bbox"]
            class_name = detection["class_name"]
            confidence = detection["confidence"]
            
            # 绘制边界框
            x1, y1, x2, y2 = int(bbox["x1"]), int(bbox["y1"]), int(bbox["x2"]), int(bbox["y2"])
            cv2.rectangle(annotated_image, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
            # 绘制标签
            label = f"{class_name}: {confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
            cv2.rectangle(annotated_image, (x1, y1 - label_size[1] - 10), 
                         (x1 + label_size[0], y1), (0, 255, 0), -1)
            cv2.putText(annotated_image, label, (x1, y1 - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 2)
        
        return annotated_image
        
    except Exception as e:
        logger.error(f"图像标注失败: {str(e)}")
        return image

# API路由定义

@app.route('/', methods=['GET'])
def index():
    """主页 - API调用统计"""
    try:
        # 计算总体统计（从数据库获取）
        api_call_stats = db_manager.get_total_api_calls() if db_manager else {'total_calls': 0, 'successful_calls': 0, 'failed_calls': 0}
        total_calls = api_call_stats['total_calls']
        total_success = api_call_stats['successful_calls']
        total_errors = api_call_stats['failed_calls']
        overall_success_rate = round((total_success / total_calls * 100), 2) if total_calls > 0 else 0.0

        # 计算误检相关统计
        false_positive_stats = {
            'total_requests': 0,
            'total_false_positives': 0,
            'overall_false_positive_rate': 0.0
        }
        
        if db_manager:
            try:
                # 获取所有时间的综合统计
                all_stats = db_manager.get_combined_daily_statistics(days=365)  # 获取一年的数据
                if all_stats:
                    total_requests = sum(stat['request_count'] for stat in all_stats)
                    total_false_positives = sum(stat['false_positive_count'] for stat in all_stats)
                    
                    false_positive_stats = {
                        'total_requests': total_requests,
                        'total_false_positives': total_false_positives,
                        'overall_false_positive_rate': round((total_false_positives / total_requests * 100), 2) if total_requests > 0 else 0.0
                    }
            except Exception as e:
                logger.error(f"获取误检统计失败: {e}")

        # 获取启用的统计配置
        enabled_stats = get_enabled_stats()

        # 获取推理时间统计
        timing_stats = get_timing_statistics()

        # 获取最近15天的综合统计数据
        combined_stats = []
        if db_manager:
            try:
                combined_stats = db_manager.get_combined_daily_statistics(days=15)
            except Exception as e:
                logger.error(f"获取综合统计数据失败: {e}")
                combined_stats = []

        html_template = """
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>算法在线推理服务 - 接口调用统计</title>
            <style>
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    line-height: 1.6;
                    margin: 0;
                    padding: 20px;
                    background-color: #f5f5f5;
                }
                .container {
                    max-width: 1200px;
                    margin: 0 auto;
                    background: white;
                    padding: 30px;
                    border-radius: 10px;
                    box-shadow: 0 0 20px rgba(0,0,0,0.1);
                }
                h1 {
                    color: #333;
                    text-align: center;
                    margin-bottom: 30px;
                    padding-bottom: 10px;
                    border-bottom: 3px solid #007bff;
                }
                h2 {
                    color: #007bff;
                    margin-top: 30px;
                }
                .stats-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: 20px;
                    margin: 20px 0;
                }
                .stat-card {
                    background: #f8f9fa;
                    padding: 20px;
                    border-radius: 10px;
                    border-left: 4px solid #007bff;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                }
                .stat-card h3 {
                    margin-top: 0;
                    color: #333;
                    display: flex;
                    align-items: center;
                    gap: 10px;
                }
                .endpoint-badge {
                    display: inline-block;
                    padding: 4px 8px;
                    border-radius: 3px;
                    color: white;
                    font-weight: bold;
                    font-size: 12px;
                }
                .endpoint-badge.get { background-color: #28a745; }
                .endpoint-badge.post { background-color: #007bff; }
                .stat-number {
                    font-size: 2em;
                    font-weight: bold;
                    color: #007bff;
                    margin: 10px 0;
                }
                .success-rate {
                    font-size: 1.5em;
                    font-weight: bold;
                    margin: 10px 0;
                }
                .success-rate.high { color: #28a745; }
                .success-rate.medium { color: #ffc107; }
                .success-rate.low { color: #dc3545; }
                .stat-detail {
                    display: flex;
                    justify-content: space-between;
                    margin: 5px 0;
                    padding: 5px 0;
                    border-bottom: 1px solid #dee2e6;
                }
                .stat-detail:last-child {
                    border-bottom: none;
                }
                .status {
                    text-align: center;
                    padding: 20px;
                    background: #d4edda;
                    border: 1px solid #c3e6cb;
                    border-radius: 5px;
                    margin-bottom: 20px;
                }
                .status.error {
                    background: #f8d7da;
                    border-color: #f5c6cb;
                }
                .refresh-btn {
                    background: #007bff;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    cursor: pointer;
                    font-size: 16px;
                    margin: 20px auto;
                    display: block;
                }
                .refresh-btn:hover {
                    background: #0056b3;
                }
                .summary-card {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 20px;
                    border-radius: 10px;
                    margin-bottom: 20px;
                    text-align: center;
                }
                .summary-stat {
                    display: inline-block;
                    margin: 0 20px;
                }
                .summary-number {
                    font-size: 2.5em;
                    font-weight: bold;
                    display: block;
                }
                .summary-label {
                    font-size: 0.9em;
                    opacity: 0.9;
                }
                .chart-container {
                    background: white;
                    padding: 20px;
                    border-radius: 10px;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                    margin: 20px 0;
                }
                .chart-title {
                    text-align: center;
                    color: #333;
                    margin-bottom: 20px;
                    font-size: 1.2em;
                    font-weight: bold;
                }
                .chart-wrapper {
                    position: relative;
                    height: 400px;
                    width: 100%;
                }
                .chart-legend {
                    display: flex;
                    justify-content: center;
                    gap: 20px;
                    margin-top: 15px;
                }
                .legend-item {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                }
                .legend-color {
                    width: 16px;
                    height: 16px;
                    border-radius: 3px;
                }
                .view-toggle {
                    text-align: center;
                    margin: 20px 0;
                }
                .toggle-btn {
                    background: #007bff;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    cursor: pointer;
                    margin: 0 10px;
                    font-size: 14px;
                }
                .toggle-btn.active {
                    background: #0056b3;
                }
                .toggle-btn:hover {
                    background: #0056b3;
                }
                .table-container {
                    background: white;
                    padding: 20px;
                    border-radius: 10px;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                    margin: 20px 0;
                    overflow-x: auto;
                }
                .stats-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-top: 15px;
                }
                .stats-table th,
                .stats-table td {
                    padding: 12px;
                    text-align: center;
                    border-bottom: 1px solid #dee2e6;
                }
                .stats-table th {
                    background-color: #f8f9fa;
                    font-weight: bold;
                    color: #333;
                }
                .stats-table tr:hover {
                    background-color: #f5f5f5;
                }
                .rate-cell {
                    font-weight: bold;
                }
                .rate-low { color: #28a745; }
                .rate-medium { color: #ffc107; }
                .rate-high { color: #dc3545; }
                .hidden {
                    display: none;
                }
            </style>
            <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
            <script>
                function refreshStats() {
                    location.reload();
                }

                // 自动刷新 (每30秒)
                setInterval(refreshStats, 30000);

                // 图表数据
                const chartData = {{ combined_stats_json | safe }};
                
                // 初始化图表
                function initChart() {
                    const ctx = document.getElementById('dailyStatsChart');
                    if (!ctx) return;
                    
                    // 准备数据
                    const labels = chartData.map(item => item.date).reverse();
                    const requestData = chartData.map(item => item.request_count).reverse();
                    const falsePositiveData = chartData.map(item => item.false_positive_count).reverse();
                    const rateData = chartData.map(item => item.false_positive_rate).reverse();
                    
                    new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: labels,
                            datasets: [
                                {
                                    label: '请求数量',
                                    data: requestData,
                                    borderColor: '#007bff',
                                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                                    borderWidth: 2,
                                    fill: false,
                                    yAxisID: 'y'
                                },
                                {
                                    label: '误报数量',
                                    data: falsePositiveData,
                                    borderColor: '#dc3545',
                                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                                    borderWidth: 2,
                                    fill: false,
                                    yAxisID: 'y'
                                },
                                {
                                    label: '误报率 (%)',
                                    data: rateData,
                                    borderColor: '#ffc107',
                                    backgroundColor: 'rgba(255, 193, 7, 0.1)',
                                    borderWidth: 2,
                                    fill: false,
                                    yAxisID: 'y1',
                                    hidden: false
                                }
                            ]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            interaction: {
                                mode: 'index',
                                intersect: false,
                            },
                            plugins: {
                                title: {
                                    display: true,
                                    text: '最近15天请求数量与误报统计'
                                },
                                legend: {
                                    display: true,
                                    position: 'top'
                                },
                                tooltip: {
                                    callbacks: {
                                        afterBody: function(context) {
                                            const dataIndex = context[0].dataIndex;
                                            const rate = rateData[dataIndex];
                                            return `误报率: ${rate}%`;
                                        }
                                    }
                                }
                            },
                            scales: {
                                x: {
                                    display: true,
                                    title: {
                                        display: true,
                                        text: '日期'
                                    }
                                },
                                y: {
                                    type: 'linear',
                                    display: true,
                                    position: 'left',
                                    title: {
                                        display: true,
                                        text: '数量'
                                    }
                                },
                                y1: {
                                    type: 'linear',
                                    display: true,
                                    position: 'right',
                                    title: {
                                        display: true,
                                        text: '误报率 (%)'
                                    },
                                    grid: {
                                        drawOnChartArea: false,
                                    },
                                    min: 0,
                                    max: 100
                                }
                            }
                        }
                    });
                }

                // 页面加载完成后初始化图表和表格
                document.addEventListener('DOMContentLoaded', function() {
                    initChart();
                    initTable();
                });

                // 切换视图
                function toggleView(viewType) {
                    const chartContainer = document.getElementById('chartContainer');
                    const tableContainer = document.getElementById('tableContainer');
                    const chartBtn = document.getElementById('chartBtn');
                    const tableBtn = document.getElementById('tableBtn');
                    
                    if (viewType === 'chart') {
                        chartContainer.classList.remove('hidden');
                        tableContainer.classList.add('hidden');
                        chartBtn.classList.add('active');
                        tableBtn.classList.remove('active');
                    } else {
                        chartContainer.classList.add('hidden');
                        tableContainer.classList.remove('hidden');
                        chartBtn.classList.remove('active');
                        tableBtn.classList.add('active');
                    }
                }

                // 初始化表格
                function initTable() {
                    const tableBody = document.getElementById('statsTableBody');
                    if (!tableBody || !chartData || chartData.length === 0) return;
                    
                    // 清空表格
                    tableBody.innerHTML = '';
                    
                    // 按日期排序（从新到旧）
                    const sortedData = chartData.sort((a, b) => new Date(b.date) - new Date(a.date));
                    
                    sortedData.forEach(item => {
                        const row = document.createElement('tr');
                        
                        // 确定误报率的颜色类
                        let rateClass = 'rate-low';
                        if (item.false_positive_rate >= 10) {
                            rateClass = 'rate-high';
                        } else if (item.false_positive_rate >= 5) {
                            rateClass = 'rate-medium';
                        }
                        
                        row.innerHTML = `
                            <td>${item.date}</td>
                            <td>${item.request_count}</td>
                            <td>${item.false_positive_count}</td>
                            <td class="rate-cell ${rateClass}">${item.false_positive_rate}%</td>
                        `;
                        
                        tableBody.appendChild(row);
                    });
                }
            </script>
        </head>
        <body>
            <div class="container">
                <h1>📊 算法在线推理服务 - 接口调用统计</h1>

                <div class="status {{ 'error' if not model_loaded else '' }}">
                    <h3>服务状态</h3>
                    <p><strong>模型状态:</strong> {{ '❌ 未加载' if not model_loaded else '✅ 已加载' }}</p>
                    <p><strong>服务时间:</strong> {{ current_time }}</p>
                </div>

                <div class="summary-card">
                    <div class="summary-stat">
                        <span class="summary-number">{{ false_positive_stats.total_requests }}</span>
                        <span class="summary-label">总请求次数</span>
                    </div>
                    <div class="summary-stat">
                        <span class="summary-number">{{ false_positive_stats.total_false_positives }}</span>
                        <span class="summary-label">误检次数</span>
                    </div>
                    <div class="summary-stat">
                        <span class="summary-number">{{ false_positive_stats.overall_false_positive_rate }}%</span>
                        <span class="summary-label">总体误检率</span>
                    </div>
                    <div class="summary-stat">
                        <span class="summary-number">{{ total_calls }}</span>
                        <span class="summary-label">API调用次数</span>
                    </div>
                </div>

                {{ timing_stats_html | safe }}

                {{ daily_chart_html | safe }}

                <h2>📈 接口调用详情</h2>

                <div class="stats-grid">
                    {{ stats_cards_html | safe }}
                </div>

                <button class="refresh-btn" onclick="refreshStats()">🔄 刷新统计</button>

                <h2>🔗 快速测试</h2>
                <div class="stat-card">
                    <p><a href="/health" target="_blank">🔍 检查服务健康状态</a></p>
                    <p><a href="/false-positive-viewer" target="_blank">🖼️ 误报图像浏览器</a></p>
                    <p><strong>说明:</strong> 页面每30秒自动刷新统计数据</p>
                </div>
            </div>
        </body>
        </html>
        """

        # 动态生成统计卡片HTML
        stats_cards_html = ""
        for endpoint, config in enabled_stats.items():
            if endpoint in api_stats:
                stats = api_stats[endpoint]
                success_rate = get_success_rate(endpoint)
                success_rate_class = 'high' if success_rate >= 95 else 'medium' if success_rate >= 80 else 'low'
                method_class = config['method'].lower()

                stats_cards_html += f"""
                    <div class="stat-card">
                        <h3>
                            <span class="endpoint-badge {method_class}">{config['method']}</span>
                            /{endpoint}
                        </h3>
                        <p style="color: #666; font-size: 0.9em; margin: 5px 0;">{config['description']}</p>
                        <div class="stat-number">{stats['total']}</div>
                        <div class="success-rate {success_rate_class}">
                            成功率: {success_rate}%
                        </div>
                        <div class="stat-detail">
                            <span>成功次数:</span>
                            <span>{stats['success']}</span>
                        </div>
                        <div class="stat-detail">
                            <span>错误次数:</span>
                            <span>{stats['errors']}</span>
                        </div>
                        <div class="stat-detail">
                            <span>总调用次数:</span>
                            <span>{stats['total']}</span>
                        </div>
                    </div>
                """

        # 生成推理时间统计HTML
        timing_stats_html = ""
        if timing_stats.get('total_inferences', 0) > 0:
            overall = timing_stats['overall']
            recent = timing_stats['recent_10']

            timing_stats_html = f"""
                <h2>⏱️ 推理时间统计 (排除预热)</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>📊 总体统计</h3>
                        <div class="stat-detail">
                            <span>推理次数:</span>
                            <span>{timing_stats['total_inferences']}</span>
                        </div>
                        <div class="stat-detail">
                            <span>平均总时间:</span>
                            <span>{overall['avg_total_time_ms']} ms</span>
                        </div>
                        <div class="stat-detail">
                            <span>最快总时间:</span>
                            <span>{overall['min_total_time_ms']} ms</span>
                        </div>
                        <div class="stat-detail">
                            <span>最慢总时间:</span>
                            <span>{overall['max_total_time_ms']} ms</span>
                        </div>
                        <div class="stat-detail">
                            <span>平均推理时间:</span>
                            <span>{overall['avg_inference_time_ms']} ms</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <h3>🔥 最近10次统计</h3>
                        <div class="stat-detail">
                            <span>平均总时间:</span>
                            <span>{recent['avg_total_time_ms']} ms</span>
                        </div>
                        <div class="stat-detail">
                            <span>最快总时间:</span>
                            <span>{recent['min_total_time_ms']} ms</span>
                        </div>
                        <div class="stat-detail">
                            <span>最慢总时间:</span>
                            <span>{recent['max_total_time_ms']} ms</span>
                        </div>
                        <div class="stat-detail">
                            <span>平均推理时间:</span>
                            <span>{recent['avg_inference_time_ms']} ms</span>
                        </div>
                    </div>
                </div>
            """
        else:
            timing_stats_html = f"""
                <h2>⏱️ 推理时间统计</h2>
                <div class="stat-card">
                    <p style="text-align: center; color: #666; font-style: italic;">
                        {timing_stats.get('message', '暂无推理数据')}
                    </p>
                </div>
            """

        # 生成每日统计图表HTML
        daily_chart_html = ""
        if combined_stats:
            daily_chart_html = f"""
                <h2>📊 每日请求与误报统计 (最近15天)</h2>
                
                <div class="view-toggle">
                    <button id="tableBtn" class="toggle-btn active" onclick="toggleView('table')">📋 表格视图</button>
                    <button id="chartBtn" class="toggle-btn" onclick="toggleView('chart')">📈 折线图</button>
                </div>
                
                <!-- 表格视图 -->
                <div id="tableContainer" class="table-container">
                    <div class="chart-title">每日统计数据表格</div>
                    <table class="stats-table">
                        <thead>
                            <tr>
                                <th>日期</th>
                                <th>请求数量</th>
                                <th>误报数量</th>
                                <th>误报率</th>
                            </tr>
                        </thead>
                        <tbody id="statsTableBody">
                            <!-- 数据将通过JavaScript动态填充 -->
                        </tbody>
                    </table>
                </div>
                
                <!-- 图表视图 -->
                <div id="chartContainer" class="chart-container hidden">
                    <div class="chart-title">请求数量 vs 误报数量 vs 误报率</div>
                    <div class="chart-wrapper">
                        <canvas id="dailyStatsChart"></canvas>
                    </div>
                    <div class="chart-legend">
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #007bff;"></div>
                            <span>请求数量</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #dc3545;"></div>
                            <span>误报数量</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #ffc107;"></div>
                            <span>误报率 (%)</span>
                        </div>
                    </div>
                </div>
            """
        else:
            daily_chart_html = f"""
                <h2>📊 每日请求与误报统计</h2>
                <div class="stat-card">
                    <p style="text-align: center; color: #666; font-style: italic;">
                        暂无统计数据
                    </p>
                </div>
            """

        return render_template_string(html_template,
                                    model_loaded=model is not None,
                                    current_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                    total_calls=total_calls,
                                    total_success=total_success,
                                    total_errors=total_errors,
                                    overall_success_rate=overall_success_rate,
                                    false_positive_stats=false_positive_stats,
                                    stats_cards_html=stats_cards_html,
                                    timing_stats_html=timing_stats_html,
                                    daily_chart_html=daily_chart_html,
                                    combined_stats_json=json.dumps(combined_stats, ensure_ascii=False))

    except Exception as e:
        update_api_stats('index', False)
        logger.error(f"主页访问错误: {str(e)}")
        return f"服务器错误: {str(e)}", 500

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    try:
        status = {
            "status": "healthy" if model is not None else "unhealthy",
            "model_loaded": model is not None,
            "model_path": model_path,
            "timestamp": datetime.now().isoformat(),
            "supported_formats": list(ALLOWED_EXTENSIONS)
        }

        return jsonify(status), 200 if model is not None else 503

    except Exception as e:
        logger.error(f"健康检查错误: {str(e)}")
        return jsonify({
            "error": "健康检查失败",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

@app.route('/predict', methods=['POST'])
def predict():
    """图像推理端点"""
    # 记录总开始时间
    total_start_time = time.time()
    timing_info = {}

    try:
        # 检查模型是否已加载
        if model is None:
            update_api_stats('predict', False)
            return jsonify({
                "error": "模型未加载",
                "message": "YOLO模型未正确加载，请检查模型文件"
            }), 503

        # 检查是否有文件上传
        if 'image' not in request.files:
            update_api_stats('predict', False)
            return jsonify({
                "error": "缺少图像文件",
                "message": "请在请求中包含名为'image'的文件"
            }), 400

        file = request.files['image']
        if file.filename == '':
            update_api_stats('predict', False)
            return jsonify({
                "error": "未选择文件",
                "message": "请选择要上传的图像文件"
            }), 400

        # 检查文件格式
        if not allowed_file(file.filename):
            update_api_stats('predict', False)
            return jsonify({
                "error": "不支持的文件格式",
                "message": f"支持的格式: {', '.join(ALLOWED_EXTENSIONS)}",
                "uploaded_format": file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else "unknown"
            }), 400

        # 获取参数
        confidence_threshold = float(request.form.get('confidence', 0.1))
        return_image = request.form.get('return_image', 'false').lower() == 'true'

        # 生成会话ID
        session_id = db_manager.generate_session_id() if db_manager else None

        # 获取客户端信息
        client_ip = request.remote_addr
        user_agent = request.headers.get('User-Agent', '')

        # 读取和预处理图像
        preprocess_start = time.time()
        image_data = file.read()
        
        # 保存原始图像
        original_save_info = image_storage.save_image_from_bytes(
            image_data, session_id, file.filename, "original"
        )
        original_image_path = original_save_info['relative_path']


        image_array, pil_image = preprocess_image(image_data)
        cv2.cvtColor(image_array, cv2.COLOR_RGB2BGR,image_array)
        cv2.imwrite("test.jpg", image_array)
        preprocess_end = time.time()
        timing_info['preprocess_time'] = round((preprocess_end - preprocess_start) * 1000, 2)  # 毫秒

        # 执行推理
        inference_start = time.time()
        inference_info = {
            "filename": file.filename,
            "image_size": {"width": pil_image.width, "height": pil_image.height},
            "confidence_threshold": confidence_threshold,
            "return_image": return_image
        }
        logger.info(f"开始推理: {json.dumps(inference_info, ensure_ascii=False)}")
        image = Image.open(io.BytesIO(image_data))
        results = model(image)
        inference_end = time.time()
        timing_info['inference_time'] = round((inference_end - inference_start) * 1000, 2)  # 毫秒

        # 后处理结果
        postprocess_start = time.time()
        detections = postprocess_results(results, confidence_threshold)
        postprocess_end = time.time()
        timing_info['postprocess_time'] = round((postprocess_end - postprocess_start) * 1000, 2)  # 毫秒
        
        # 如果需要返回标注图像，记录图像处理时间
        image_annotation_time = 0
        annotated_image = None
        if return_image and detections:
            annotation_start = time.time()
            annotated_image = create_annotated_image(image_array, detections)
            # 转换为base64编码
            _, buffer = cv2.imencode('.jpg', annotated_image)
            image_base64 = base64.b64encode(buffer).decode('utf-8')
            annotation_end = time.time()
            image_annotation_time = round((annotation_end - annotation_start) * 1000, 2)  # 毫秒

        # 计算总时间
        total_end_time = time.time()
        timing_info['image_annotation_time'] = image_annotation_time
        timing_info['total_time'] = round((total_end_time - total_start_time) * 1000, 2)  # 毫秒

        # 保存数据到数据库和图像存储
        original_image_path = None
        annotated_image_path = None

        try:
            if db_manager and image_storage and session_id:
                

                # 保存原始图像信息到数据库
                db_manager.save_image_info(
                    session_id=session_id,
                    image_type="original",
                    file_path=original_save_info['storage_path'],
                    file_name=original_save_info['filename'],
                    file_size=original_save_info['file_size'],
                    file_format=original_save_info['format'],
                    width=original_save_info['width'],
                    height=original_save_info['height'],
                    channels=original_save_info['channels'],
                    file_data=image_data
                )

                # 如果有标注图像，也保存
                if annotated_image is not None:
                    annotated_save_info = image_storage.save_image_from_array(
                        annotated_image, session_id, file.filename, "annotated"
                    )
                    annotated_image_path = annotated_save_info['relative_path']

                    # 保存标注图像信息到数据库
                    db_manager.save_image_info(
                        session_id=session_id,
                        image_type="annotated",
                        file_path=annotated_save_info['storage_path'],
                        file_name=annotated_save_info['filename'],
                        file_size=annotated_save_info['file_size'],
                        file_format=annotated_save_info['format'],
                        width=annotated_save_info['width'],
                        height=annotated_save_info['height'],
                        channels=annotated_save_info['channels']
                    )

                # 保存推理记录
                db_manager.save_inference_record(
                    session_id=session_id,
                    filename=file.filename,
                    file_size=len(image_data),
                    image_width=pil_image.width,
                    image_height=pil_image.height,
                    model_path=model_path,
                    confidence_threshold=confidence_threshold,
                    detections_count=len(detections),
                    inference_timestamp=datetime.now().isoformat(),
                    timing_info=timing_info,
                    original_image_path=original_image_path,
                    annotated_image_path=annotated_image_path,
                    has_annotated_image=annotated_image is not None,
                    client_ip=client_ip,
                    user_agent=user_agent,
                    status='success'
                )

                # 保存检测结果
                if detections:
                    db_manager.save_detection_results(session_id, detections)

                logger.info(f"数据保存成功: session_id={session_id}")

        except Exception as save_error:
            logger.error(f"保存数据失败: {save_error}")
            # 不影响主要的推理响应，只记录错误

        # 准备响应数据
        response_data = {
            "success": True,
            "session_id": session_id,
            "timestamp": datetime.now().isoformat(),
            "filename": file.filename,
            "image_size": {
                "width": pil_image.width,
                "height": pil_image.height
            },
            "model_path": model_path,
            "confidence_threshold": confidence_threshold,
            "detections_count": len(detections),
            "detections": detections,
            "timing": {
                "preprocess_time_ms": timing_info['preprocess_time'],
                "inference_time_ms": timing_info['inference_time'],
                "postprocess_time_ms": timing_info['postprocess_time'],
                "image_annotation_time_ms": timing_info['image_annotation_time'],
                "total_time_ms": timing_info['total_time'],
                "breakdown": {
                    "preprocess_percentage": round((timing_info['preprocess_time'] / timing_info['total_time']) * 100, 1),
                    "inference_percentage": round((timing_info['inference_time'] / timing_info['total_time']) * 100, 1),
                    "postprocess_percentage": round((timing_info['postprocess_time'] / timing_info['total_time']) * 100, 1),
                    "annotation_percentage": round((timing_info['image_annotation_time'] / timing_info['total_time']) * 100, 1) if timing_info['image_annotation_time'] > 0 else 0
                }
            },
            "storage": {
                "original_image_path": original_image_path,
                "annotated_image_path": annotated_image_path,
                "has_stored_images": original_image_path is not None
            }
        }

        # 如果需要返回标注图像
        if return_image and detections:
            response_data["annotated_image"] = f"data:image/jpeg;base64,{image_base64}"

        # 记录响应摘要（避免打印完整base64）
        response_summary = log_response_summary(response_data)
        logger.info(f"推理完成: {json.dumps(response_summary, ensure_ascii=False, indent=2)}")

        # 更新推理时间统计（排除预热推理）
        update_inference_timing_stats(timing_info)

        # 更新成功统计
        update_api_stats('predict', True)
        return jsonify(response_data)

    except Exception as e:
        # 更新错误统计
        update_api_stats('predict', False)

        error_info = {
            "error_type": type(e).__name__,
            "error_message": str(e),
            "timestamp": datetime.now().isoformat(),
            "filename": file.filename if 'file' in locals() else "unknown"
        }
        logger.error(f"推理过程中发生错误: {json.dumps(error_info, ensure_ascii=False, indent=2)}")
        logger.error(f"错误堆栈: {traceback.format_exc()}")

        return jsonify({
            "error": "推理失败",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

@app.errorhandler(413)
def too_large(e):
    """文件过大错误处理"""
    return jsonify({
        "error": "文件过大",
        "message": "上传的文件超过16MB限制"
    }), 413

@app.route('/api/records', methods=['GET'])
def get_inference_records():
    """获取推理记录列表"""
    try:
        if not db_manager:
            return jsonify({
                "error": "数据库未初始化",
                "message": "数据库管理器未正确初始化"
            }), 503

        # 获取查询参数
        limit = int(request.args.get('limit', 50))
        status = request.args.get('status')  # success, error

        # 查询记录
        records = db_manager.get_recent_inferences(limit=limit, status=status)

        return jsonify({
            "success": True,
            "count": len(records),
            "records": records,
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"查询推理记录失败: {e}")
        return jsonify({
            "error": "查询失败",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

@app.route('/api/records/<session_id>', methods=['GET'])
def get_inference_record_detail(session_id):
    """获取特定推理记录的详细信息"""
    try:
        if not db_manager:
            return jsonify({
                "error": "数据库未初始化",
                "message": "数据库管理器未正确初始化"
            }), 503

        # 获取推理记录
        record = db_manager.get_inference_record(session_id)
        if not record:
            return jsonify({
                "error": "记录不存在",
                "message": f"未找到会话ID为 {session_id} 的记录"
            }), 404

        # 获取检测结果
        detections = db_manager.get_detection_results(session_id)

        # 获取图像信息
        images = db_manager.get_image_info(session_id)

        return jsonify({
            "success": True,
            "record": record,
            "detections": detections,
            "images": images,
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"查询推理记录详情失败: {e}")
        return jsonify({
            "error": "查询失败",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

@app.route('/api/statistics/daily', methods=['GET'])
def get_daily_statistics():
    """获取每日统计数据"""
    try:
        if not db_manager:
            return jsonify({
                "error": "数据库未初始化",
                "message": "数据库管理器未正确初始化"
            }), 503

        # 获取查询参数
        days = int(request.args.get('days', 30))

        # 查询统计数据
        stats = db_manager.get_daily_statistics(days=days)

        return jsonify({
            "success": True,
            "days": days,
            "statistics": stats,
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"查询每日统计失败: {e}")
        return jsonify({
            "error": "查询失败",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

@app.route('/api/statistics/classes', methods=['GET'])
def get_class_statistics():
    """获取类别统计数据"""
    try:
        if not db_manager:
            return jsonify({
                "error": "数据库未初始化",
                "message": "数据库管理器未正确初始化"
            }), 503

        # 查询类别统计
        stats = db_manager.get_class_statistics()

        return jsonify({
            "success": True,
            "class_count": len(stats),
            "statistics": stats,
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"查询类别统计失败: {e}")
        return jsonify({
            "error": "查询失败",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

@app.route('/api/statistics/database', methods=['GET'])
def get_database_statistics():
    """获取数据库统计信息"""
    try:
        if not db_manager or not image_storage:
            return jsonify({
                "error": "存储系统未初始化",
                "message": "数据库或图像存储管理器未正确初始化"
            }), 503

        # 获取数据库统计
        db_stats = db_manager.get_database_stats()

        # 获取图像存储统计
        storage_stats = image_storage.get_storage_statistics()

        return jsonify({
            "success": True,
            "database": db_stats,
            "storage": storage_stats,
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"查询数据库统计失败: {e}")
        return jsonify({
            "error": "查询失败",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

@app.route('/api/images/<session_id>/<image_type>', methods=['GET'])
def get_image(session_id, image_type):
    """获取存储的图像文件"""
    try:
        if not db_manager or not image_storage:
            return jsonify({
                "error": "存储系统未初始化",
                "message": "数据库或图像存储管理器未正确初始化"
            }), 503

        # 验证图像类型
        if image_type not in ['original', 'annotated', 'thumbnail']:
            return jsonify({
                "error": "无效的图像类型",
                "message": "支持的类型: original, annotated, thumbnail"
            }), 400

        # 获取图像信息
        images = db_manager.get_image_info(session_id, image_type)
        if not images:
            return jsonify({
                "error": "图像不存在",
                "message": f"未找到会话ID为 {session_id} 的 {image_type} 图像"
            }), 404

        image_info = images[0]  # 取第一个匹配的图像

        # 检查文件是否存在
        if not image_storage.image_exists(image_info['file_path']):
            return jsonify({
                "error": "图像文件不存在",
                "message": "图像文件可能已被删除"
            }), 404

        # 返回图像文件
        from flask import send_file
        full_path = image_storage.get_image_path(image_info['file_path'])
        return send_file(full_path, as_attachment=False)

    except Exception as e:
        logger.error(f"获取图像失败: {e}")
        return jsonify({
            "error": "获取图像失败",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

@app.route('/api/false-positive/report', methods=['POST'])
def report_false_positive():
    """上报误判接口 - 简化版，只需要session_id"""
    try:
        if not db_manager:
            return jsonify({
                "error": "数据库未初始化",
                "message": "数据库管理器未正确初始化"
            }), 503

        # 获取请求数据
        data = request.get_json()
        if not data:
            return jsonify({
                "error": "无效的请求数据",
                "message": "请求体必须包含JSON数据"
            }), 400

        # 验证必需字段（只需要session_id）
        if 'session_id' not in data:
            return jsonify({
                "error": "缺少必需字段: session_id",
                "message": "请提供session_id"
            }), 400

        session_id = data['session_id']

        # 验证session_id是否存在
        inference_record = db_manager.get_inference_record(session_id)
        if not inference_record:
            return jsonify({
                "error": "推理记录不存在",
                "message": f"未找到会话ID为 {session_id} 的推理记录"
            }), 404

        # 生成报告ID
        import uuid
        report_id = str(uuid.uuid4())

        # 获取客户端信息
        client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', 'unknown'))
        user_agent = request.headers.get('User-Agent', 'unknown')

        # 插入到数据库（简化版）
        try:
            with db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO false_positive_reports (
                        session_id, report_id, false_positive_type, client_ip, user_agent
                    ) VALUES (?, ?, ?, ?, ?)
                """, (
                    session_id, report_id, 'general', client_ip, user_agent
                ))
                conn.commit()
            
            logger.info(f"误判上报成功: {report_id} for session {session_id}")
            
            return jsonify({
                "success": True,
                "message": "误判上报成功",
                "report_id": report_id,
                "session_id": session_id,
                "timestamp": datetime.now().isoformat()
            }), 201
            
        except Exception as db_error:
            logger.error(f"数据库插入失败: {db_error}")
            return jsonify({
                "error": "数据库操作失败",
                "message": str(db_error)
            }), 500

    except Exception as e:
        logger.error(f"误判上报失败: {e}")
        return jsonify({
            "error": "误判上报失败",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

@app.route('/api/false-positive/list', methods=['GET'])
def list_false_positive_reports():
    """获取误判上报列表"""
    try:
        if not db_manager:
            return jsonify({
                "error": "数据库未初始化",
                "message": "数据库管理器未正确初始化"
            }), 503

        page = int(request.args.get('page', 1))
        limit = min(int(request.args.get('limit', 50)), 100)
        status = request.args.get('status', '')

        # 新增：时间过滤参数
        start_time = request.args.get('start_time')
        end_time = request.args.get('end_time')

        offset = (page - 1) * limit

        where_clause = ""
        params = []
        if status:
            where_clause = "WHERE status = ?"
            params.append(status)

        # 新增：拼接时间过滤
        if start_time:
            where_clause += (" AND " if where_clause else "WHERE ") + "created_at >= ?"
            params.append(start_time)
        if end_time:
            where_clause += (" AND " if where_clause else "WHERE ") + "created_at <= ?"
            params.append(end_time)

        # 查询总数
        count_query = f"SELECT COUNT(*) FROM false_positive_reports {where_clause}"
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(count_query, params)
            total_count = cursor.fetchone()[0]

            # 查询数据
            query = f"""
                SELECT fpr.*, ir.filename, ir.created_at as inference_created_at
                FROM false_positive_reports fpr
                LEFT JOIN inference_records ir ON fpr.session_id = ir.session_id
                {where_clause}
                ORDER BY fpr.created_at DESC
                LIMIT ? OFFSET ?
            """
            params.extend([limit, offset])
            
            cursor.execute(query, params)
            reports = [dict(row) for row in cursor.fetchall()]

        return jsonify({
            "success": True,
            "data": reports,
            "pagination": {
                "page": page,
                "limit": limit,
                "total": total_count,
                "pages": (total_count + limit - 1) // limit
            },
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"获取误判上报列表失败: {e}")
        return jsonify({
            "error": "获取误判上报列表失败",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

@app.route('/api/maintenance/cleanup', methods=['POST'])
def cleanup_old_data():
    """清理旧数据"""
    try:
        if not db_manager or not image_storage:
            return jsonify({
                "error": "存储系统未初始化",
                "message": "数据库或图像存储管理器未正确初始化"
            }), 503

        # 获取参数
        days_to_keep = int(request.json.get('days_to_keep', 90))

        # 清理数据库记录
        db_cleanup = db_manager.cleanup_old_records(days_to_keep)

        # 清理图像文件
        storage_cleanup = image_storage.cleanup_old_images(days_to_keep)

        return jsonify({
            "success": True,
            "days_to_keep": days_to_keep,
            "database_cleanup": {
                "deleted_inferences": db_cleanup[0],
                "deleted_detections": db_cleanup[1],
                "deleted_image_info": db_cleanup[2]
            },
            "storage_cleanup": storage_cleanup,
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"清理数据失败: {e}")
        return jsonify({
            "error": "清理失败",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

# 误报图像浏览器路由
PAGE_SIZE = 20

FALSE_POSITIVE_VIEWER_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>误报图像浏览</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 10px;
            border-bottom: 3px solid #007bff;
        }
        .filter-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .filter-form input[type="date"] {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .filter-form button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .filter-form button:hover {
            background: #0056b3;
        }
        .report-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .report-card {
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            background: #fafbfc;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        .report-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0,0,0,0.15);
        }
        .img-preview {
            width: 140px;
            height: 140px;
            object-fit: contain;
            border: 1px solid #eee;
            border-radius: 4px;
            margin-right: 12px;
            cursor: pointer;
        }
        .img-row {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .meta {
            font-size: 13px;
            color: #666;
        }
        .meta div {
            margin-bottom: 4px;
            padding: 2px 0;
        }
        .meta strong {
            color: #333;
        }
        .pagination {
            text-align: center;
            margin-top: 30px;
            padding: 20px 0;
        }
        .pagination a {
            margin: 0 8px;
            text-decoration: none;
            color: #007bff;
            padding: 8px 16px;
            border: 1px solid #007bff;
            border-radius: 4px;
            transition: all 0.2s;
        }
        .pagination a:hover {
            background: #007bff;
            color: white;
        }
        .pagination .current {
            font-weight: bold;
            color: #333;
            padding: 8px 16px;
            background: #e9ecef;
            border: 1px solid #dee2e6;
            border-radius: 4px;
        }
        .no-data {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 40px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .status-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-confirmed { background: #d4edda; color: #155724; }
        .status-rejected { background: #f8d7da; color: #721c24; }
        .type-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
            background: #e7f3ff;
            color: #0066cc;
        }
        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            color: #007bff;
            text-decoration: none;
            font-size: 14px;
        }
        .back-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="/" class="back-link">← 返回主页</a>
        <h1>🔍 误报图像浏览器</h1>

        <form method="get" class="filter-form">
            <label for="date">筛选日期:</label>
            <input type="date" id="date" name="date" value="{{ date }}">
            <button type="submit">筛选</button>
            {% if date %}
            <a href="/false-positive-viewer" style="color: #6c757d; text-decoration: none; margin-left: 10px;">清除筛选</a>
            {% endif %}
        </form>

        {% if reports %}
        <div class="report-list">
            {% for report in reports %}
            <div class="report-card">
                <div class="img-row">
                    {% if report.original_image_url %}
                    <img src="{{ report.original_image_url }}" class="img-preview" title="点击查看原图" onclick="window.open(this.src)">
                    {% endif %}
                    {% if report.annotated_image_url %}
                    <img src="{{ report.annotated_image_url }}" class="img-preview" title="点击查看标注图" onclick="window.open(this.src)">
                    {% endif %}
                </div>
                <div class="meta">
                    <div><strong>报告ID:</strong> {{ report.report_id }}</div>
                    <div><strong>会话ID:</strong> {{ report.session_id }}</div>
                    <div><strong>类型:</strong> <span class="type-badge">{{ report.false_positive_type }}</span></div>
                    <div><strong>状态:</strong> <span class="status-badge status-{{ report.status }}">{{ report.status }}</span></div>
                    <div><strong>上报时间:</strong> {{ report.created_at }}</div>
                    {% if report.description %}
                    <div><strong>描述:</strong> {{ report.description }}</div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="no-data">
            {% if date %}
            📅 {{ date }} 当天暂无误报记录
            {% else %}
            📝 暂无误报记录
            {% endif %}
        </div>
        {% endif %}

        {% if reports %}
        <div class="pagination">
            {% if page > 1 %}
            <a href="?date={{ date }}&page={{ page-1 }}">← 上一页</a>
            {% endif %}
            <span class="current">第 {{ page }} 页</span>
            {% if has_next %}
            <a href="?date={{ date }}&page={{ page+1 }}">下一页 →</a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</body>
</html>
'''

@app.route('/false-positive-viewer')
def false_positive_viewer():
    """误报图像浏览器"""
    try:
        if not db_manager or not image_storage:
            return jsonify({
                "error": "存储系统未初始化",
                "message": "数据库或图像存储系统未正确初始化"
            }), 503

        # 获取日期和分页参数
        date_str = request.args.get('date')
        page = int(request.args.get('page', 1))
        offset = (page - 1) * PAGE_SIZE

        # 构建查询条件
        where = []
        params = []
        if date_str:
            where.append("DATE(fpr.created_at) = ?")
            params.append(date_str)
        where_clause = 'WHERE ' + ' AND '.join(where) if where else ''

        # 查询误报记录
        query = f'''
            SELECT fpr.*, ir.original_image_path, ir.annotated_image_path, ir.has_annotated_image
            FROM false_positive_reports fpr
            LEFT JOIN inference_records ir ON fpr.session_id = ir.session_id
            {where_clause}
            ORDER BY fpr.created_at DESC
            LIMIT ? OFFSET ?
        '''
        params += [PAGE_SIZE + 1, offset]  # 多查一条判断是否有下一页

        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(query, params)
            rows = cursor.fetchall()

        reports = []
        for row in rows[:PAGE_SIZE]:
            original_url = None
            annotated_url = None

            if row['original_image_path'] and image_storage.image_exists(row['original_image_path']):
                original_url = f"/image/original/{row['original_image_path']}"
            if row['annotated_image_path'] and row['has_annotated_image'] and image_storage.image_exists(row['annotated_image_path']):
                annotated_url = f"/image/annotated/{row['annotated_image_path']}"

            reports.append({
                'report_id': row['report_id'],
                'session_id': row['session_id'],
                'false_positive_type': row['false_positive_type'],
                'status': row['status'],
                'created_at': row['created_at'],
                'description': row.get('description', ''),
                'original_image_url': original_url,
                'annotated_image_url': annotated_url
            })

        has_next = len(rows) > PAGE_SIZE

        return render_template_string(
            FALSE_POSITIVE_VIEWER_TEMPLATE,
            reports=reports,
            page=page,
            has_next=has_next,
            date=date_str or ''
        )

    except Exception as e:
        logger.error(f"误报浏览器错误: {str(e)}")
        return jsonify({
            "error": "误报浏览器错误",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

@app.route('/image/<img_type>/<path:rel_path>')
def serve_image(img_type, rel_path):
    """提供图像文件服务"""
    try:
        if not image_storage:
            abort(404)

        # 安全检查
        if '..' in rel_path or rel_path.startswith('/'):
            abort(404)

        # 验证图像类型
        if img_type not in ['original', 'annotated']:
            abort(404)

        base_dir = os.path.join(image_storage.base_path, img_type)
        abs_path = os.path.join(base_dir, rel_path)

        if not os.path.isfile(abs_path):
            abort(404)

        return send_from_directory(base_dir, rel_path)

    except Exception as e:
        logger.error(f"图像服务错误: {str(e)}")
        abort(404)

@app.errorhandler(404)
def not_found(e):
    """404错误处理"""
    return jsonify({
        "error": "端点不存在",
        "message": "请访问 / 查看可用的API端点"
    }), 404

if __name__ == '__main__':
    print("=" * 60)
    print("YOLO推理API服务器启动中...")
    print("=" * 60)

    # 初始化存储系统
    if not initialize_storage_systems():
        print("❌ 存储系统初始化失败！")
        print("请检查数据库架构文件和存储目录权限。")
        exit(1)

    print("✅ 存储系统初始化成功")

    # 加载模型
    if not load_model():
        print("❌ 模型加载失败！请检查模型文件是否存在。")
        print("支持的模型文件位置:")
        print("  - YOLO_outputs/*/weights/best.pt")
        print("  - yolo11n.pt, yolov8n.pt 等预训练模型")
        exit(1)

    print(f"✅ 模型加载成功: {model_path}")

    # 模型预热
    print("🔥 开始模型预热...")
    if warmup_model():
        print("✅ 模型预热完成")
    else:
        print("⚠️ 模型预热失败，但不影响服务启动")

    print(f"🚀 服务器启动地址: http://localhost:5001")
    print(f"📖 API文档: http://localhost:5001")
    print(f"🔍 健康检查: http://localhost:5001/health")
    print(f"💾 数据库: yolo_inference.db")
    print(f"📁 图像存储: image_storage/")
    print("=" * 60)

    # 启动Flask服务器
    app.run(host='0.0.0.0', port=5001, debug=False)
