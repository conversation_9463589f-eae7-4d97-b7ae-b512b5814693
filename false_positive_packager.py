#!/usr/bin/env python3
"""
误判图像数据打包脚本
用于收集误判上报相关的图像数据并打包成ZIP文件

使用方法:
1. 打包所有误判数据: python false_positive_packager.py
2. 打包特定状态的误判数据: python false_positive_packager.py --status pending
3. 打包特定日期范围的误判数据: python false_positive_packager.py --start-date 2024-01-01 --end-date 2024-12-31
4. 指定输出目录: python false_positive_packager.py --output-dir /path/to/output
"""

import os
import sys
import json
import zipfile
import argparse
import sqlite3
from datetime import datetime, timedelta
from pathlib import Path
import shutil
import logging

# 导入项目模块
from database_manager import DatabaseManager
from image_storage import ImageStorage

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class FalsePositivePackager:
    """误判数据打包器"""
    
    def __init__(self, db_path="yolo_inference.db", image_storage_dir="image_storage"):
        """
        初始化打包器
        
        Args:
            db_path: 数据库文件路径
            image_storage_dir: 图像存储目录路径
        """
        self.db_path = db_path
        self.image_storage_dir = image_storage_dir
        self.db_manager = None
        self.image_storage = None
        
    def initialize(self):
        """初始化数据库和图像存储管理器"""
        try:
            # 初始化数据库管理器
            self.db_manager = DatabaseManager(self.db_path)
            logger.info(f"数据库连接成功: {self.db_path}")
            
            # 初始化图像存储管理器
            self.image_storage = ImageStorage(self.image_storage_dir)
            logger.info(f"图像存储初始化成功: {self.image_storage_dir}")
            
            return True
            
        except Exception as e:
            logger.error(f"初始化失败: {e}")
            return False
    
    def get_false_positive_reports(self, status=None, start_date=None, end_date=None):
        """
        获取误判上报记录
        
        Args:
            status: 状态过滤 (pending, confirmed, rejected, processed)
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            
        Returns:
            list: 误判上报记录列表
        """
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # 构建查询条件
                where_conditions = []
                params = []
                
                if status:
                    where_conditions.append("fpr.status = ?")
                    params.append(status)
                    
                if start_date:
                    where_conditions.append("DATE(fpr.created_at) >= ?")
                    params.append(start_date)
                    
                if end_date:
                    where_conditions.append("DATE(fpr.created_at) <= ?")
                    params.append(end_date)
                
                where_clause = ""
                if where_conditions:
                    where_clause = "WHERE " + " AND ".join(where_conditions)
                
                # 查询误判上报记录及相关信息
                query = f"""
                    SELECT 
                        fpr.*,
                        ir.filename,
                        ir.file_size,
                        ir.image_width,
                        ir.image_height,
                        ir.model_path,
                        ir.confidence_threshold,
                        ir.detections_count,
                        ir.inference_timestamp,
                        ir.original_image_path,
                        ir.annotated_image_path,
                        ir.has_annotated_image
                    FROM false_positive_reports fpr
                    LEFT JOIN inference_records ir ON fpr.session_id = ir.session_id
                    {where_clause}
                    ORDER BY fpr.created_at DESC
                """
                
                cursor.execute(query, params)
                reports = [dict(row) for row in cursor.fetchall()]
                
                logger.info(f"找到 {len(reports)} 条误判上报记录")
                return reports
                
        except Exception as e:
            logger.error(f"查询误判上报记录失败: {e}")
            return []
    
    def get_detection_results(self, session_id):
        """
        获取特定会话的检测结果
        
        Args:
            session_id: 会话ID
            
        Returns:
            list: 检测结果列表
        """
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM detection_results 
                    WHERE session_id = ?
                    ORDER BY detection_index
                """, (session_id,))
                
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            logger.error(f"查询检测结果失败: {e}")
            return []
    
    def copy_image_to_package(self, image_path, package_dir, new_filename):
        """
        复制图像到打包目录
        
        Args:
            image_path: 原始图像路径
            package_dir: 打包目录
            new_filename: 新文件名
            
        Returns:
            bool: 是否成功复制
        """
        try:
            if not image_path or not self.image_storage.image_exists(image_path):
                return False
                
            full_source_path = self.image_storage.get_image_path(image_path)
            target_path = os.path.join(package_dir, new_filename)
            
            # 确保目标目录存在
            os.makedirs(os.path.dirname(target_path), exist_ok=True)
            
            # 复制文件
            shutil.copy2(full_source_path, target_path)
            return True
            
        except Exception as e:
            logger.error(f"复制图像失败 {image_path}: {e}")
            return False
    
    def create_package(self, reports, output_dir="false_positive_packages"):
        """
        创建误判数据包
        
        Args:
            reports: 误判上报记录列表
            output_dir: 输出目录
            
        Returns:
            str: 生成的ZIP文件路径
        """
        if not reports:
            logger.warning("没有找到误判上报记录，无法创建数据包")
            return None
            
        try:
            # 创建输出目录
            output_path = Path(output_dir)
            output_path.mkdir(exist_ok=True)
            
            # 生成包名称
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            package_name = f"false_positive_data_{timestamp}"
            package_dir = output_path / package_name
            package_dir.mkdir(exist_ok=True)
            
            # 创建子目录
            images_dir = package_dir / "images"
            images_dir.mkdir(exist_ok=True)
            
            metadata_dir = package_dir / "metadata"
            metadata_dir.mkdir(exist_ok=True)
            
            # 处理每个误判上报记录
            package_summary = {
                "created_at": datetime.now().isoformat(),
                "total_reports": len(reports),
                "reports": []
            }
            
            for i, report in enumerate(reports):
                logger.info(f"处理误判上报 {i+1}/{len(reports)}: {report['report_id']}")
                
                session_id = report['session_id']
                report_id = report['report_id']
                
                # 创建报告专用目录
                report_dir = images_dir / f"report_{report_id}"
                report_dir.mkdir(exist_ok=True)
                
                # 复制原始图像
                original_copied = False
                if report['original_image_path']:
                    original_filename = f"original_{session_id}.jpg"
                    original_copied = self.copy_image_to_package(
                        report['original_image_path'],
                        str(report_dir),
                        original_filename
                    )
                
                # 复制标注图像
                annotated_copied = False
                if report['annotated_image_path'] and report['has_annotated_image']:
                    annotated_filename = f"annotated_{session_id}.jpg"
                    annotated_copied = self.copy_image_to_package(
                        report['annotated_image_path'],
                        str(report_dir),
                        annotated_filename
                    )
                
                # 获取检测结果
                detection_results = self.get_detection_results(session_id)
                
                # 创建报告元数据
                report_metadata = {
                    "report_info": {
                        "report_id": report_id,
                        "session_id": session_id,
                        "false_positive_type": report['false_positive_type'],
                        "reported_class_name": report['reported_class_name'],
                        "correct_class_name": report['correct_class_name'],
                        "report_reason": report['report_reason'],
                        "report_description": report['report_description'],
                        "reporter_info": report['reporter_info'],
                        "status": report['status'],
                        "created_at": report['created_at']
                    },
                    "inference_info": {
                        "filename": report['filename'],
                        "file_size": report['file_size'],
                        "image_dimensions": {
                            "width": report['image_width'],
                            "height": report['image_height']
                        },
                        "model_path": report['model_path'],
                        "confidence_threshold": report['confidence_threshold'],
                        "detections_count": report['detections_count'],
                        "inference_timestamp": report['inference_timestamp']
                    },
                    "detection_results": detection_results,
                    "files": {
                        "original_image": original_filename if original_copied else None,
                        "annotated_image": annotated_filename if annotated_copied else None
                    }
                }
                
                # 保存报告元数据
                metadata_file = metadata_dir / f"report_{report_id}.json"
                with open(metadata_file, 'w', encoding='utf-8') as f:
                    json.dump(report_metadata, f, ensure_ascii=False, indent=2)
                
                # 添加到包摘要
                package_summary["reports"].append({
                    "report_id": report_id,
                    "session_id": session_id,
                    "false_positive_type": report['false_positive_type'],
                    "has_original_image": original_copied,
                    "has_annotated_image": annotated_copied,
                    "detections_count": len(detection_results)
                })
            
            # 保存包摘要
            summary_file = package_dir / "package_summary.json"
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(package_summary, f, ensure_ascii=False, indent=2)
            
            # 创建README文件
            readme_content = f"""# 误判数据包

## 包信息
- 创建时间: {package_summary['created_at']}
- 误判上报数量: {package_summary['total_reports']}
- 包名称: {package_name}

## 目录结构
```
{package_name}/
├── images/                     # 图像文件
│   └── report_<report_id>/     # 每个报告的图像
│       ├── original_<session_id>.jpg    # 原始图像
│       └── annotated_<session_id>.jpg   # 标注图像
├── metadata/                   # 元数据文件
│   └── report_<report_id>.json # 每个报告的详细信息
├── package_summary.json        # 包摘要信息
└── README.md                   # 说明文档
```

## 使用说明
1. images/ 目录包含所有相关的图像文件
2. metadata/ 目录包含每个误判上报的详细信息，包括推理结果和检测数据
3. package_summary.json 提供整个包的概览信息

## 误判类型说明
- false_detection: 误检测（检测到不存在的对象）
- wrong_class: 错误分类（对象存在但分类错误）
- low_confidence: 低置信度（检测正确但置信度过低）
"""
            
            readme_file = package_dir / "README.md"
            with open(readme_file, 'w', encoding='utf-8') as f:
                f.write(readme_content)
            
            # 创建ZIP文件
            zip_path = output_path / f"{package_name}.zip"
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for file_path in package_dir.rglob('*'):
                    if file_path.is_file():
                        arcname = file_path.relative_to(package_dir)
                        zipf.write(file_path, arcname)
            
            # 删除临时目录
            shutil.rmtree(package_dir)
            
            logger.info(f"误判数据包创建成功: {zip_path}")
            logger.info(f"包大小: {zip_path.stat().st_size / (1024*1024):.2f} MB")
            
            return str(zip_path)
            
        except Exception as e:
            logger.error(f"创建数据包失败: {e}")
            return None

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="误判图像数据打包脚本")
    parser.add_argument("--status", choices=["pending", "confirmed", "rejected", "processed"],
                       help="过滤特定状态的误判上报")
    parser.add_argument("--start-date", help="开始日期 (YYYY-MM-DD)")
    parser.add_argument("--end-date", help="结束日期 (YYYY-MM-DD)")
    parser.add_argument("--output-dir", default="false_positive_packages",
                       help="输出目录 (默认: false_positive_packages)")
    parser.add_argument("--db-path", default="yolo_inference.db",
                       help="数据库文件路径 (默认: yolo_inference.db)")
    parser.add_argument("--image-storage-dir", default="image_storage",
                       help="图像存储目录 (默认: image_storage)")
    
    args = parser.parse_args()
    
    # 创建打包器
    packager = FalsePositivePackager(args.db_path, args.image_storage_dir)
    
    # 初始化
    if not packager.initialize():
        logger.error("初始化失败，退出程序")
        sys.exit(1)
    
    # 获取误判上报记录
    logger.info("正在查询误判上报记录...")
    reports = packager.get_false_positive_reports(
        status=args.status,
        start_date=args.start_date,
        end_date=args.end_date
    )
    
    if not reports:
        logger.warning("没有找到符合条件的误判上报记录")
        sys.exit(0)
    
    # 创建数据包
    logger.info(f"开始打包 {len(reports)} 条误判上报记录...")
    zip_path = packager.create_package(reports, args.output_dir)
    
    if zip_path:
        print(f"\n✅ 误判数据包创建成功!")
        print(f"📁 文件路径: {zip_path}")
        print(f"📊 包含记录: {len(reports)} 条")
        
        # 显示统计信息
        status_counts = {}
        type_counts = {}
        for report in reports:
            status = report['status']
            fp_type = report['false_positive_type']
            status_counts[status] = status_counts.get(status, 0) + 1
            type_counts[fp_type] = type_counts.get(fp_type, 0) + 1
        
        print(f"\n📈 状态分布:")
        for status, count in status_counts.items():
            print(f"  - {status}: {count}")
            
        print(f"\n🏷️  类型分布:")
        for fp_type, count in type_counts.items():
            print(f"  - {fp_type}: {count}")
            
    else:
        logger.error("数据包创建失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
