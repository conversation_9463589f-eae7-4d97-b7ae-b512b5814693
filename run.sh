#!/bin/bash

# YOLO API Server 管理脚本
# 用法: ./run.sh {start|stop|restart|status}

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PID_FILE="$SCRIPT_DIR/yolo_api_server.pid"
LOG_FILE="$SCRIPT_DIR/error.log"
PYTHON_SCRIPT="$SCRIPT_DIR/yolo_api_server.py"

# 检查进程是否运行
is_running() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0  # 运行中
        else
            rm -f "$PID_FILE"  # 清理无效的PID文件
            return 1  # 未运行
        fi
    else
        return 1  # 未运行
    fi
}

# 启动服务
start_server() {
    if is_running; then
        local pid=$(cat "$PID_FILE")
        echo "YOLO API Server 已经在运行中 (PID: $pid)"
        return 1
    fi

    echo "启动 YOLO API Server..."
    cd "$SCRIPT_DIR"
    nohup python "$PYTHON_SCRIPT" 1>/dev/null 2>"$LOG_FILE" &
    local pid=$!
    echo $pid > "$PID_FILE"

    # 等待一下检查是否启动成功
    sleep 2
    if is_running; then
        echo "YOLO API Server 启动成功 (PID: $pid)"
        echo "日志文件: $LOG_FILE"
        return 0
    else
        echo "YOLO API Server 启动失败，请检查日志: $LOG_FILE"
        return 1
    fi
}

# 停止服务
stop_server() {
    if ! is_running; then
        echo "YOLO API Server 未运行"
        return 1
    fi

    local pid=$(cat "$PID_FILE")
    echo "停止 YOLO API Server (PID: $pid)..."

    # 尝试优雅停止
    kill "$pid" 2>/dev/null

    # 等待进程结束
    local count=0
    while [ $count -lt 10 ]; do
        if ! ps -p "$pid" > /dev/null 2>&1; then
            rm -f "$PID_FILE"
            echo "YOLO API Server 已停止"
            return 0
        fi
        sleep 1
        count=$((count + 1))
    done

    # 如果优雅停止失败，强制停止
    echo "强制停止 YOLO API Server..."
    kill -9 "$pid" 2>/dev/null
    rm -f "$PID_FILE"
    echo "YOLO API Server 已强制停止"
    return 0
}

# 重启服务
restart_server() {
    echo "重启 YOLO API Server..."
    stop_server
    sleep 1
    start_server
}

# 查看状态
show_status() {
    if is_running; then
        local pid=$(cat "$PID_FILE")
        echo "YOLO API Server 状态: 运行中"
        echo "PID: $pid"
        echo "日志文件: $LOG_FILE"

        # 显示进程信息
        if command -v ps > /dev/null; then
            echo "进程信息:"
            ps -p "$pid" -o pid,ppid,cmd,etime,pcpu,pmem 2>/dev/null || echo "无法获取进程详细信息"
        fi

        # 显示最近的日志
        if [ -f "$LOG_FILE" ] && [ -s "$LOG_FILE" ]; then
            echo ""
            echo "最近的错误日志 (最后10行):"
            tail -10 "$LOG_FILE"
        fi
    else
        echo "YOLO API Server 状态: 未运行"

        # 如果有日志文件，显示最近的错误
        if [ -f "$LOG_FILE" ] && [ -s "$LOG_FILE" ]; then
            echo ""
            echo "最近的错误日志 (最后5行):"
            tail -5 "$LOG_FILE"
        fi
    fi
}

# 显示帮助信息
show_help() {
    echo "YOLO API Server 管理脚本"
    echo ""
    echo "用法: $0 {start|stop|restart|status|help}"
    echo ""
    echo "命令:"
    echo "  start   - 启动服务"
    echo "  stop    - 停止服务"
    echo "  restart - 重启服务"
    echo "  status  - 查看服务状态"
    echo "  help    - 显示此帮助信息"
    echo ""
    echo "文件:"
    echo "  PID文件: $PID_FILE"
    echo "  日志文件: $LOG_FILE"
    echo "  Python脚本: $PYTHON_SCRIPT"
}

# 主逻辑
case "${1:-}" in
    start)
        start_server
        ;;
    stop)
        stop_server
        ;;
    restart)
        restart_server
        ;;
    status)
        show_status
        ;;
    help|--help|-h)
        show_help
        ;;
    "")
        # 如果没有参数，默认启动服务（保持向后兼容）
        echo "未指定命令，默认启动服务..."
        start_server
        ;;
    *)
        echo "错误: 未知命令 '$1'"
        echo ""
        show_help
        exit 1
        ;;
esac